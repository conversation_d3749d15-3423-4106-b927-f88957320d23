# ZJU 4D Radar-Camera Dataset Integration for Marigold

## 概述 (Overview)

本项目为Marigold深度估计模型添加了ZJU 4D雷达-相机数据集的支持，实现了RGB + 深度 + 雷达数据的多模态融合。

This project adds support for the ZJU 4D Radar-Camera dataset to the Marigold depth estimation model, implementing multimodal fusion of RGB + Depth + Radar data.

## 数据格式 (Data Format)

### 输入数据格式
每一行包含三列路径：
```
image/1687163764634577152.png gt_interp/1687163764634577152.png radar_png/1687163764634577152.png
```

### 数据规格
- **RGB图像**: 1280×720, 3通道, 0-255范围
- **深度图**: 1280×720, 单通道, 256倍编码的PNG格式
- **雷达图**: 1280×720, 单通道, 256倍编码的PNG格式, 有效范围约300

### 有效区域
所有数据的有效区域为高度方向的 `[:, 240:540]` (即 `height//3` 到 `height//4*3`)

## 核心功能 (Key Features)

### 1. 雷达数据VAE编码
- 将单通道雷达数据复制为3通道格式
- 使用预训练VAE编码器将雷达数据编码到潜在空间
- 输出4维潜在特征用于与RGB和深度特征融合

### 2. 多模态特征融合
- RGB潜在特征: [B, 4, h, w]
- 深度潜在特征: [B, 4, h, w] 
- 雷达潜在特征: [B, 4, h, w]
- 融合后特征: [B, 12, h, w]

### 3. UNet架构扩展
- 自动扩展UNet输入层从4通道到12通道
- 保持预训练权重，新增通道初始化为零
- 支持有/无雷达数据的训练

## 使用方法 (Usage)

### 1. 数据准备
```bash
# 更新数据列表文件，添加雷达数据路径
python update_data_lists.py
```

### 2. 训练模型
```bash
# 使用ZJU雷达数据集训练
python train_zju.py --config config/train_zju_radar.yaml
```

### 3. 配置文件
- `config/train_zju_radar.yaml`: 主训练配置
- `config/dataset/zju_dataset.yaml`: 数据集配置

## 代码结构 (Code Structure)

### 主要修改文件
1. **train_zju.py**: ZJU数据集训练脚本
   - `ZJUDataset`: 自定义数据集类
   - `_load_radar_data()`: 雷达数据加载
   - `_read_radar_file()`: 雷达文件读取

2. **src/trainer/marigold_trainer.py**: 训练器扩展
   - `encode_radar()`: 雷达VAE编码
   - `stack_radar_images()`: 雷达图像堆叠
   - 特征融合逻辑

3. **update_data_lists.py**: 数据列表更新脚本

### 关键函数说明

#### 雷达数据处理流程
```python
def _load_radar_data(self, radar_rel_path):
    """
    雷达数据处理流程:
    1. 读取雷达PNG文件 (1280x720, 单通道, 256倍编码)
    2. 解码为实际距离值 (有效范围约300)
    3. 裁剪到有效区域 [:, 240:540]
    4. 归一化到[-1, 1]范围，为VAE编码做准备
    """
```

#### VAE编码
```python
def encode_radar(self, radar_in):
    """
    将雷达数据通过VAE编码器编码到潜在空间:
    1. 将单通道雷达数据复制为3通道格式
    2. 使用与RGB相同的VAE编码器进行编码
    3. 输出4维潜在特征，用于与RGB和深度特征融合
    """
```

#### 特征融合
```python
# 构建连接的潜在特征: RGB + 噪声深度 + 雷达
latents_to_concat = [rgb_latent, noisy_latents]
if radar_latent is not None:
    latents_to_concat.append(radar_latent)
    cat_latents = torch.cat(latents_to_concat, dim=1)  # [B, 12, h, w]
```

## 技术细节 (Technical Details)

### VAE编码策略
- 重用预训练的RGB VAE编码器
- 将单通道雷达数据复制为3通道
- 保持与RGB数据相同的编码流程

### 输入通道扩展
- UNet输入从4通道扩展到12通道
- 新增权重初始化为零，保持训练稳定性
- 支持渐进式训练策略

### 数据增强
- 同步应用于RGB、深度和雷达数据
- 保持空间对应关系
- 支持随机裁剪、翻转等操作

## 注意事项 (Notes)

1. **内存使用**: 12通道输入会增加内存使用，建议适当调整批次大小
2. **训练稳定性**: 新增通道初始化为零，确保训练初期稳定
3. **数据对齐**: 确保RGB、深度和雷达数据在空间上严格对齐
4. **有效区域**: 所有数据处理都需要考虑有效区域裁剪

## 性能优化建议 (Performance Tips)

1. 使用混合精度训练减少内存使用
2. 适当调整学习率和批次大小
3. 考虑使用梯度累积处理大批次
4. 监控训练过程中的内存使用情况
