#!/usr/bin/env python3
"""
训练测试脚本
Training test script

用于测试ZJU雷达数据集的训练流程
"""

import os
import sys
import time
import torch
from pathlib import Path

def check_environment():
    """检查训练环境"""
    print("=== 检查训练环境 ===")
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("❌ CUDA不可用")
        return False
    
    # 检查数据目录
    data_dir = "/data/Baidu/ZJU-4DRadarCam/data/"
    if os.path.exists(data_dir):
        print(f"✅ 数据目录存在: {data_dir}")
    else:
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    # 检查数据列表文件
    train_list = "data_split/zju/train_list.txt"
    val_list = "data_split/zju/val_list.txt"
    
    if os.path.exists(train_list):
        with open(train_list, 'r') as f:
            train_count = len(f.readlines())
        print(f"✅ 训练列表: {train_count} 样本")
    else:
        print(f"❌ 训练列表不存在: {train_list}")
        return False
    
    if os.path.exists(val_list):
        with open(val_list, 'r') as f:
            val_count = len(f.readlines())
        print(f"✅ 验证列表: {val_count} 样本")
    else:
        print(f"❌ 验证列表不存在: {val_list}")
        return False
    
    # 检查配置文件
    config_file = "config/train_zju_radar.yaml"
    if os.path.exists(config_file):
        print(f"✅ 配置文件存在: {config_file}")
    else:
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    return True

def test_data_loading():
    """测试数据加载"""
    print("\n=== 测试数据加载 ===")
    
    try:
        sys.path.append('.')
        from train_zju import ZJUDataset
        from src.dataset import DatasetMode
        from omegaconf import OmegaConf
        from src.util.depth_transform import get_depth_normalizer
        
        # 加载配置
        cfg = OmegaConf.load("config/train_zju_radar.yaml")
        
        # 创建深度归一化器
        depth_transform = get_depth_normalizer(cfg.depth_normalization)
        print(f"深度归一化器类型: {type(depth_transform)}")
        
        # 创建数据集
        dataset = ZJUDataset(
            mode=DatasetMode.TRAIN,
            filename_ls_path="data_split/zju/train_list.txt",
            dataset_dir="/data/Baidu/ZJU-4DRadarCam/data/",
            disp_name="zju_test",
            depth_transform=depth_transform,
            resize_to_hw=[300, 1280]
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        # 测试数据加载
        sample = dataset[0]
        print(f"样本类型: {type(sample)}, 长度: {len(sample)}")

        if len(sample) == 2:
            rasters, other = sample
        else:
            rasters = sample
            other = None

        print("数据键值:", list(rasters.keys()))
        for key, value in rasters.items():
            if hasattr(value, 'shape'):
                print(f"  {key}: {value.shape}, range: [{value.min():.3f}, {value.max():.3f}]")
        
        # 检查雷达数据
        if "radar_norm" in rasters:
            print("✅ 雷达数据加载成功")
        else:
            print("❌ 雷达数据加载失败")
            return False
        
        print("✅ 数据加载测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n=== 测试模型加载 ===")
    
    try:
        from marigold.marigold_pipeline import MarigoldPipeline
        
        print("正在加载预训练模型...")
        model = MarigoldPipeline.from_pretrained("stabilityai/stable-diffusion-2")
        print("✅ 模型加载成功")
        
        # 检查模型组件
        print(f"UNet输入通道: {model.unet.config['in_channels']}")
        print(f"VAE潜在维度: {model.vae.config['latent_channels']}")
        
        return model
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_short_training_test():
    """运行短时间训练测试"""
    print("\n=== 运行短时间训练测试 ===")
    
    try:
        # 创建测试输出目录
        test_output_dir = "./output/training_test"
        os.makedirs(test_output_dir, exist_ok=True)
        
        # 构建训练命令
        cmd = [
            "python", "train_zju.py",
            "--config", "config/train_zju_radar.yaml",
            "--base_data_dir", "/data/Baidu/ZJU-4DRadarCam/data/",
            "--base_ckpt_dir", "./checkpoint",
            "--output_dir", test_output_dir,
            "--add_datetime_prefix"
        ]
        
        print(f"训练命令: {' '.join(cmd)}")
        print("开始短时间训练测试 (将在30秒后停止)...")
        
        import subprocess
        import signal
        
        # 启动训练进程
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # 设置30秒超时
        start_time = time.time()
        timeout = 30
        
        output_lines = []
        while True:
            # 检查超时
            if time.time() - start_time > timeout:
                print(f"\n⏰ 达到{timeout}秒超时，停止训练测试")
                process.terminate()
                break
            
            # 读取输出
            line = process.stdout.readline()
            if line:
                output_lines.append(line.strip())
                print(line.strip())
            
            # 检查进程是否结束
            if process.poll() is not None:
                break
        
        # 等待进程结束
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
        
        # 分析输出
        success_indicators = [
            "Model loaded successfully",
            "Training started",
            "Epoch",
            "Loss:",
            "lr:"
        ]
        
        found_indicators = []
        for indicator in success_indicators:
            for line in output_lines:
                if indicator.lower() in line.lower():
                    found_indicators.append(indicator)
                    break
        
        print(f"\n训练测试结果:")
        print(f"找到的成功指标: {found_indicators}")
        
        if len(found_indicators) >= 2:
            print("✅ 训练测试通过 - 训练流程正常启动")
            return True
        else:
            print("❌ 训练测试失败 - 训练流程未正常启动")
            return False
        
    except Exception as e:
        print(f"❌ 训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始ZJU雷达数据集训练测试...\n")
    
    success = True
    
    # 检查环境
    success &= check_environment()
    
    if not success:
        print("\n❌ 环境检查失败，无法继续测试")
        return False
    
    # 测试数据加载
    success &= test_data_loading()
    
    if not success:
        print("\n❌ 数据加载测试失败，无法继续测试")
        return False
    
    # 测试模型加载
    model = test_model_loading()
    success &= (model is not None)
    
    if not success:
        print("\n❌ 模型加载测试失败，无法继续测试")
        return False
    
    # 运行训练测试
    success &= run_short_training_test()
    
    if success:
        print("\n🎉 所有训练测试通过！")
        print("\n测试总结:")
        print("✅ 环境检查通过")
        print("✅ 数据加载正常")
        print("✅ 模型加载成功")
        print("✅ 训练流程启动正常")
        
        print("\n现在可以开始正式训练:")
        print("bash run_zju_training.sh")
        print("或者:")
        print("python train_zju.py --config config/train_zju_radar.yaml")
        
    else:
        print("\n❌ 部分训练测试失败，请检查问题")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
