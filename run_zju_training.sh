#!/bin/bash

# ZJU Dataset Training Script for Marigold
# Usage: ./run_zju_training.sh [options]

# Default values
CONFIG="config/train_zju_radar.yaml"  # 使用新的雷达配置文件
DATA_DIR="/data/Baidu/ZJU-4DRadarCam/data/"
OUTPUT_DIR="./output"
NO_WANDB=""
RESUME=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --config)
            CONFIG="$2"
            shift 2
            ;;
        --data_dir)
            DATA_DIR="$2"
            shift 2
            ;;
        --output_dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --no_wandb)
            NO_WANDB="--no_wandb"
            shift
            ;;
        --resume)
            RESUME="--resume_run $2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --config CONFIG_FILE    Configuration file (default: config/train_zju_radar.yaml)"
            echo "  --data_dir DATA_DIR     Data directory (default: /data/Baidu/ZJU-4DRadarCam/data/)"
            echo "  --output_dir OUTPUT_DIR Output directory (default: ./output)"
            echo "  --no_wandb              Disable wandb logging"
            echo "  --resume CHECKPOINT     Resume from checkpoint"
            echo "  --help                  Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Set environment variables
export BASE_DATA_DIR="$DATA_DIR"
export BASE_CKPT_DIR="./checkpoint"

# Print configuration
echo "=== ZJU 4D Radar-Camera Dataset Training Configuration ==="
echo "Config file: $CONFIG"
echo "Data directory: $DATA_DIR"
echo "Output directory: $OUTPUT_DIR"
echo "Wandb: $([ -z "$NO_WANDB" ] && echo "enabled" || echo "disabled")"
echo "Resume: $([ -z "$RESUME" ] && echo "no" || echo "${RESUME#--resume_run }")"
echo "Features: RGB + Depth + Radar multimodal fusion"
echo "=========================================="

# Check if data directory exists
if [ ! -d "$DATA_DIR" ]; then
    echo "Error: Data directory does not exist: $DATA_DIR"
    echo "Please check the path or use --data_dir to specify the correct path"
    exit 1
fi

# Check if config file exists
if [ ! -f "$CONFIG" ]; then
    echo "Error: Config file does not exist: $CONFIG"
    echo "Please check the path or use --config to specify the correct path"
    exit 1
fi

# Check if train list exists
TRAIN_LIST="data_split/zju/train_list.txt"
if [ ! -f "$TRAIN_LIST" ]; then
    echo "Error: Training list file does not exist: $TRAIN_LIST"
    echo "Please make sure the ZJU data split files are in place"
    exit 1
fi

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Run training
echo "Starting ZJU dataset training..."
python train_zju.py \
    --config "$CONFIG" \
    --base_data_dir "$DATA_DIR" \
    --output_dir "$OUTPUT_DIR" \
    --add_datetime_prefix \
    $NO_WANDB \
    $RESUME

echo "Training completed!"
