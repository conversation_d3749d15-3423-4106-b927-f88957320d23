#!/usr/bin/env python3
"""
测试脚本：验证雷达数据处理功能
Test script for radar data processing functionality

用于验证：
1. 雷达数据加载和预处理
2. VAE编码功能
3. 特征融合
4. 数据格式正确性
"""

import os
import sys
import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# 添加项目路径
sys.path.append('.')

from train_zju import ZJUDataset
from src.dataset import DatasetMode
from src.util.depth_transform import get_depth_normalizer
from omegaconf import OmegaConf

def test_radar_data_loading():
    """测试雷达数据加载功能"""
    print("=== 测试雷达数据加载 ===")
    
    # 创建测试配置
    cfg_normalizer = OmegaConf.create({
        "type": "scale_shift_depth",
        "norm_min": -1.0,
        "norm_max": 1.0,
        "min_max_quantile": 0.02,
        "clip": True
    })
    
    depth_transform = get_depth_normalizer(cfg_normalizer)
    
    # 创建ZJU数据集实例
    dataset = ZJUDataset(
        mode=DatasetMode.TRAIN,
        filename_ls_path="data_split/zju/train_list.txt",
        dataset_dir="/data/Baidu/ZJU-4DRadarCam/data/",
        disp_name="zju_test",
        depth_transform=depth_transform,
        resize_to_hw=[300, 1280]
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 测试第一个样本
    if len(dataset) > 0:
        sample = dataset[0]
        rasters, other = sample
        
        print("数据键值:", list(rasters.keys()))
        
        # 检查各种数据的形状
        for key, value in rasters.items():
            if isinstance(value, torch.Tensor):
                print(f"{key}: {value.shape}, dtype: {value.dtype}, range: [{value.min():.3f}, {value.max():.3f}]")
        
        # 特别检查雷达数据
        if "radar_norm" in rasters:
            radar_data = rasters["radar_norm"]
            print(f"\n雷达数据详细信息:")
            print(f"  形状: {radar_data.shape}")
            print(f"  数据类型: {radar_data.dtype}")
            print(f"  数值范围: [{radar_data.min():.3f}, {radar_data.max():.3f}]")
            print(f"  均值: {radar_data.mean():.3f}")
            print(f"  标准差: {radar_data.std():.3f}")
            
            # 检查是否有NaN或Inf
            print(f"  包含NaN: {torch.isnan(radar_data).any()}")
            print(f"  包含Inf: {torch.isinf(radar_data).any()}")
        else:
            print("警告: 未找到雷达数据")
    
    return dataset

def test_vae_encoding():
    """测试VAE编码功能"""
    print("\n=== 测试VAE编码功能 ===")
    
    # 创建模拟的训练器来测试编码功能
    from marigold.marigold_pipeline import MarigoldPipeline
    from src.trainer.marigold_trainer import MarigoldTrainer
    from omegaconf import OmegaConf
    
    # 创建模拟配置
    cfg = OmegaConf.create({
        "trainer": {"init_seed": 42},
        "lr": 1e-5,
        "lr_scheduler": {
            "kwargs": {
                "total_iter": 1000,
                "final_ratio": 0.01,
                "warmup_steps": 100
            }
        }
    })
    
    # 加载模型
    model = MarigoldPipeline.from_pretrained("stabilityai/stable-diffusion-2")
    
    # 创建训练器实例
    trainer = MarigoldTrainer(
        cfg=cfg,
        model=model,
        train_dataloader=None,  # 测试时不需要
        device=torch.device("cpu"),  # 使用CPU进行测试
        base_ckpt_dir="./checkpoint",
        out_dir_ckpt="./output/test/checkpoint",
        out_dir_eval="./output/test/eval",
        out_dir_vis="./output/test/vis",
        accumulation_steps=1
    )
    
    # 创建测试雷达数据
    batch_size = 2
    height, width = 300, 1280
    radar_data = torch.randn(batch_size, 1, height, width) * 0.5  # 模拟归一化的雷达数据
    
    print(f"输入雷达数据形状: {radar_data.shape}")
    
    # 测试雷达图像堆叠
    stacked_radar = trainer.stack_radar_images(radar_data)
    print(f"堆叠后雷达数据形状: {stacked_radar.shape}")
    
    # 测试VAE编码
    with torch.no_grad():
        radar_latent = trainer.encode_radar(radar_data)
        print(f"雷达潜在特征形状: {radar_latent.shape}")
        print(f"雷达潜在特征范围: [{radar_latent.min():.3f}, {radar_latent.max():.3f}]")
    
    return trainer

def test_feature_fusion():
    """测试特征融合功能"""
    print("\n=== 测试特征融合功能 ===")
    
    # 创建模拟的潜在特征
    batch_size = 2
    latent_height, latent_width = 37, 160  # 典型的潜在空间尺寸
    
    rgb_latent = torch.randn(batch_size, 4, latent_height, latent_width)
    depth_latent = torch.randn(batch_size, 4, latent_height, latent_width)
    radar_latent = torch.randn(batch_size, 4, latent_height, latent_width)
    
    print(f"RGB潜在特征: {rgb_latent.shape}")
    print(f"深度潜在特征: {depth_latent.shape}")
    print(f"雷达潜在特征: {radar_latent.shape}")
    
    # 测试特征融合
    latents_to_concat = [rgb_latent, depth_latent, radar_latent]
    fused_latents = torch.cat(latents_to_concat, dim=1)
    
    print(f"融合后特征: {fused_latents.shape}")
    print(f"期望形状: [B, 12, H, W] = [{batch_size}, 12, {latent_height}, {latent_width}]")
    
    # 验证形状正确性
    expected_shape = (batch_size, 12, latent_height, latent_width)
    assert fused_latents.shape == expected_shape, f"形状不匹配: {fused_latents.shape} != {expected_shape}"
    print("✓ 特征融合测试通过")

def test_data_consistency():
    """测试数据一致性"""
    print("\n=== 测试数据一致性 ===")
    
    # 创建测试数据
    height, width = 720, 1280
    valid_start, valid_end = 240, 540
    
    # 模拟原始雷达数据 (PNG格式，256倍编码)
    radar_raw_png = np.random.randint(0, 256*300, (height, width), dtype=np.uint16)
    
    # 模拟解码过程
    radar_decoded = radar_raw_png.astype(np.float32) / 256.0
    
    # 模拟裁剪
    radar_cropped = radar_decoded[valid_start:valid_end, :]
    
    # 模拟归一化
    radar_max_range = 300.0
    radar_norm = (radar_cropped / radar_max_range) * 2.0 - 1.0
    radar_norm = np.clip(radar_norm, -1.0, 1.0)
    
    print(f"原始数据形状: {radar_raw_png.shape}")
    print(f"解码后形状: {radar_decoded.shape}")
    print(f"裁剪后形状: {radar_cropped.shape}")
    print(f"归一化后形状: {radar_norm.shape}")
    print(f"归一化范围: [{radar_norm.min():.3f}, {radar_norm.max():.3f}]")
    
    # 验证裁剪尺寸
    expected_height = valid_end - valid_start
    assert radar_cropped.shape[0] == expected_height, f"裁剪高度不正确: {radar_cropped.shape[0]} != {expected_height}"
    assert radar_cropped.shape[1] == width, f"裁剪宽度不正确: {radar_cropped.shape[1]} != {width}"
    
    # 验证归一化范围
    assert radar_norm.min() >= -1.0 and radar_norm.max() <= 1.0, f"归一化范围超出[-1, 1]: [{radar_norm.min()}, {radar_norm.max()}]"
    
    print("✓ 数据一致性测试通过")

def main():
    """主测试函数"""
    print("开始雷达数据处理功能测试...\n")
    
    try:
        # 测试数据一致性
        test_data_consistency()
        
        # 测试特征融合
        test_feature_fusion()
        
        # 如果有实际数据文件，测试数据加载
        if os.path.exists("data_split/zju/train_list.txt"):
            dataset = test_radar_data_loading()
        else:
            print("跳过数据加载测试 (未找到数据文件)")
        
        # 测试VAE编码 (需要下载模型)
        try:
            trainer = test_vae_encoding()
            print("✓ VAE编码测试通过")
        except Exception as e:
            print(f"跳过VAE编码测试 (模型加载失败): {e}")
        
        print("\n=== 所有测试完成 ===")
        print("✓ 雷达数据处理功能验证通过")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
