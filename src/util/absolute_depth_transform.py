"""
绝对深度归一化器
Absolute Depth Normalizer

用于将绝对深度值归一化到[-1, 1]范围，并支持反归一化
"""

import torch
import numpy as np
from .depth_transform import DepthNormalizerBase


class AbsoluteDepthNormalizer(DepthNormalizerBase):
    """
    绝对深度归一化器
    将绝对深度值线性映射到[-1, 1]范围
    """
    
    def __init__(
        self,
        depth_min: float = 0.1,      # 最小深度值 (米)
        depth_max: float = 100.0,    # 最大深度值 (米)
        norm_min: float = -1.0,      # 归一化最小值
        norm_max: float = 1.0,       # 归一化最大值
        clip: bool = True,           # 是否裁剪超出范围的值
        **kwargs
    ):
        # 不调用父类的__init__，因为它会抛出NotImplementedError
        # Don't call parent __init__ as it raises NotImplementedError
        self.norm_min = norm_min
        self.norm_max = norm_max
        
        self.depth_min = depth_min
        self.depth_max = depth_max
        self.norm_min = norm_min
        self.norm_max = norm_max
        self.clip = clip
        
        # 计算线性变换参数
        # depth_norm = scale * depth + shift
        self.scale = (norm_max - norm_min) / (depth_max - depth_min)
        self.shift = norm_min - self.scale * depth_min
        
        print(f"AbsoluteDepthNormalizer initialized:")
        print(f"  Depth range: [{depth_min}, {depth_max}] meters")
        print(f"  Norm range: [{norm_min}, {norm_max}]")
        print(f"  Transform: norm = {self.scale:.6f} * depth + {self.shift:.6f}")
    
    def __call__(self, depth, valid_mask=None):
        """
        归一化深度值
        
        Args:
            depth: 输入深度 [B, 1, H, W] 或 [H, W]，单位：米
            valid_mask: 有效掩码，可选
            
        Returns:
            归一化后的深度 [-1, 1]
        """
        # 线性归一化: [depth_min, depth_max] -> [norm_min, norm_max]
        depth_norm = self.scale * depth + self.shift
        
        if self.clip:
            depth_norm = torch.clamp(depth_norm, self.norm_min, self.norm_max)
        
        return depth_norm
    
    def denormalize(self, depth_norm):
        """
        反归一化：将归一化深度转换回绝对深度值
        
        Args:
            depth_norm: 归一化深度 [-1, 1]
            
        Returns:
            绝对深度值，单位：米
        """
        # 反向变换: depth = (depth_norm - shift) / scale
        depth = (depth_norm - self.shift) / self.scale
        
        if self.clip:
            depth = torch.clamp(depth, self.depth_min, self.depth_max)
        
        return depth
    
    def get_depth_range(self):
        """返回深度范围"""
        return self.depth_min, self.depth_max
    
    def get_norm_range(self):
        """返回归一化范围"""
        return self.norm_min, self.norm_max


class FixedRangeDepthNormalizer(DepthNormalizerBase):
    """
    固定范围深度归一化器
    兼容原有接口，但使用固定的深度范围
    """
    
    def __init__(
        self,
        norm_min: float = -1.0,
        norm_max: float = 1.0,
        depth_min: float = 0.1,
        depth_max: float = 100.0,
        clip: bool = True,
        **kwargs  # 忽略其他参数以保持兼容性
    ):
        super().__init__()
        
        self.normalizer = AbsoluteDepthNormalizer(
            depth_min=depth_min,
            depth_max=depth_max,
            norm_min=norm_min,
            norm_max=norm_max,
            clip=clip
        )
    
    def __call__(self, depth, valid_mask=None):
        """归一化深度值"""
        return self.normalizer(depth, valid_mask)
    
    def denormalize(self, depth_norm):
        """反归一化深度值"""
        return self.normalizer.denormalize(depth_norm)


def get_absolute_depth_normalizer(cfg):
    """
    获取绝对深度归一化器
    
    Args:
        cfg: 配置对象
        
    Returns:
        深度归一化器实例
    """
    if hasattr(cfg, 'type') and cfg.type == 'absolute_depth':
        return AbsoluteDepthNormalizer(
            depth_min=getattr(cfg, 'depth_min', 0.1),
            depth_max=getattr(cfg, 'depth_max', 100.0),
            norm_min=getattr(cfg, 'norm_min', -1.0),
            norm_max=getattr(cfg, 'norm_max', 1.0),
            clip=getattr(cfg, 'clip', True)
        )
    else:
        # 使用固定范围归一化器作为默认
        return FixedRangeDepthNormalizer(
            norm_min=getattr(cfg, 'norm_min', -1.0),
            norm_max=getattr(cfg, 'norm_max', 1.0),
            depth_min=getattr(cfg, 'depth_min', 0.1),
            depth_max=getattr(cfg, 'depth_max', 100.0),
            clip=getattr(cfg, 'clip', True)
        )


def test_absolute_depth_normalizer():
    """测试绝对深度归一化器"""
    print("Testing AbsoluteDepthNormalizer...")
    
    normalizer = AbsoluteDepthNormalizer(
        depth_min=0.1,
        depth_max=100.0,
        norm_min=-1.0,
        norm_max=1.0
    )
    
    # 测试数据
    test_depths = torch.tensor([0.1, 1.0, 10.0, 50.0, 100.0])
    print(f"Test depths: {test_depths}")
    
    # 归一化
    normalized = normalizer(test_depths)
    print(f"Normalized: {normalized}")
    
    # 反归一化
    denormalized = normalizer.denormalize(normalized)
    print(f"Denormalized: {denormalized}")
    
    # 验证精度
    error = torch.abs(test_depths - denormalized).max()
    print(f"Max error: {error:.6f}")
    
    assert error < 1e-5, "Normalization/denormalization error too large"
    print("✅ Test passed!")


if __name__ == "__main__":
    test_absolute_depth_normalizer()
