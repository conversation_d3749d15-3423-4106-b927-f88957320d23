#!/usr/bin/env python3
"""
纯NumPy雷达数据处理测试脚本
Pure NumPy radar data processing test script

测试核心数据处理逻辑，不依赖PyTorch
"""

import numpy as np

def test_radar_data_processing():
    """测试雷达数据处理逻辑"""
    print("=== 测试雷达数据处理逻辑 ===")
    
    # 模拟原始雷达数据 (PNG格式，256倍编码)
    height, width = 720, 1280
    # 使用合理的范围避免溢出
    max_val = min(256*300, 65535)  # uint16最大值是65535
    radar_raw_png = np.random.randint(0, max_val, (height, width), dtype=np.uint16)
    print(f"原始PNG数据形状: {radar_raw_png.shape}")
    print(f"原始PNG数据范围: [{radar_raw_png.min()}, {radar_raw_png.max()}]")
    
    # 解码过程 (除以256.0)
    radar_decoded = radar_raw_png.astype(np.float32) / 256.0
    print(f"解码后数据范围: [{radar_decoded.min():.3f}, {radar_decoded.max():.3f}]")
    
    # 裁剪到有效区域 [:, 240:540]
    valid_start, valid_end = 240, 540
    radar_cropped = radar_decoded[valid_start:valid_end, :]
    print(f"裁剪后形状: {radar_cropped.shape}")
    
    # 归一化到[-1, 1]范围
    radar_max_range = 300.0
    radar_norm = (radar_cropped / radar_max_range) * 2.0 - 1.0
    radar_norm = np.clip(radar_norm, -1.0, 1.0)
    print(f"归一化后范围: [{radar_norm.min():.3f}, {radar_norm.max():.3f}]")
    
    # 添加批次和通道维度 [1, 1, H, W]
    radar_tensor = radar_norm[np.newaxis, np.newaxis, :, :]
    print(f"张量形状: {radar_tensor.shape}")
    
    return radar_tensor

def test_radar_stacking():
    """测试雷达图像堆叠功能"""
    print("\n=== 测试雷达图像堆叠 ===")
    
    # 创建测试雷达数据
    batch_size = 2
    height, width = 300, 1280
    radar_in = np.random.randn(batch_size, 1, height, width) * 0.5  # [B, 1, H, W]
    print(f"输入雷达数据形状: {radar_in.shape}")
    
    # 堆叠为3通道 (模拟stack_radar_images函数)
    if len(radar_in.shape) == 4:
        stacked = np.repeat(radar_in, 3, axis=1)  # [B, 1, H, W] -> [B, 3, H, W]
    else:
        raise ValueError(f"不支持的雷达张量形状: {radar_in.shape}")
    
    print(f"堆叠后形状: {stacked.shape}")
    print(f"期望形状: [B, 3, H, W] = [{batch_size}, 3, {height}, {width}]")
    
    # 验证堆叠正确性
    assert stacked.shape == (batch_size, 3, height, width), f"堆叠形状错误: {stacked.shape}"
    
    # 验证所有通道数据相同
    assert np.allclose(stacked[:, 0], stacked[:, 1]), "通道0和1数据不一致"
    assert np.allclose(stacked[:, 1], stacked[:, 2]), "通道1和2数据不一致"
    
    print("✓ 雷达图像堆叠测试通过")
    return stacked

def test_feature_fusion():
    """测试特征融合功能"""
    print("\n=== 测试特征融合 ===")
    
    # 创建模拟的潜在特征
    batch_size = 2
    latent_height, latent_width = 37, 160  # 典型的潜在空间尺寸
    
    rgb_latent = np.random.randn(batch_size, 4, latent_height, latent_width)
    depth_latent = np.random.randn(batch_size, 4, latent_height, latent_width)
    radar_latent = np.random.randn(batch_size, 4, latent_height, latent_width)
    
    print(f"RGB潜在特征: {rgb_latent.shape}")
    print(f"深度潜在特征: {depth_latent.shape}")
    print(f"雷达潜在特征: {radar_latent.shape}")
    
    # 特征融合 (沿通道维度连接)
    fused_latents = np.concatenate([rgb_latent, depth_latent, radar_latent], axis=1)
    
    print(f"融合后特征: {fused_latents.shape}")
    
    # 验证融合结果
    expected_shape = (batch_size, 12, latent_height, latent_width)
    assert fused_latents.shape == expected_shape, f"融合形状错误: {fused_latents.shape} != {expected_shape}"
    
    # 验证融合内容正确性
    assert np.allclose(fused_latents[:, 0:4], rgb_latent), "RGB特征融合错误"
    assert np.allclose(fused_latents[:, 4:8], depth_latent), "深度特征融合错误"
    assert np.allclose(fused_latents[:, 8:12], radar_latent), "雷达特征融合错误"
    
    print("✓ 特征融合测试通过")
    return fused_latents

def test_fallback_fusion():
    """测试无雷达数据时的回退融合"""
    print("\n=== 测试回退融合 (无雷达数据) ===")
    
    batch_size = 2
    latent_height, latent_width = 37, 160
    
    rgb_latent = np.random.randn(batch_size, 4, latent_height, latent_width)
    depth_latent = np.random.randn(batch_size, 4, latent_height, latent_width)
    
    # 无雷达数据时，用零填充
    zero_radar = np.zeros_like(rgb_latent)  # [B, 4, h, w]
    
    fused_latents = np.concatenate([rgb_latent, depth_latent, zero_radar], axis=1)  # [B, 12, h, w]
    
    print(f"回退融合后形状: {fused_latents.shape}")
    
    # 验证零填充正确性
    assert np.allclose(fused_latents[:, 8:12], np.zeros_like(rgb_latent)), "零填充错误"
    
    print("✓ 回退融合测试通过")
    return fused_latents

def test_data_consistency():
    """测试数据一致性"""
    print("\n=== 测试数据一致性 ===")
    
    # 测试数据范围
    radar_data = np.random.randn(1, 1, 300, 1280) * 0.5
    radar_norm = np.clip(radar_data, -1.0, 1.0)
    
    assert radar_norm.min() >= -1.0, f"归一化最小值超出范围: {radar_norm.min()}"
    assert radar_norm.max() <= 1.0, f"归一化最大值超出范围: {radar_norm.max()}"
    
    # 测试尺寸一致性
    expected_height = 300  # 720 // 3 * 4 - 720 // 3 = 540 - 240 = 300
    expected_width = 1280
    
    assert radar_norm.shape[2] == expected_height, f"高度不匹配: {radar_norm.shape[2]} != {expected_height}"
    assert radar_norm.shape[3] == expected_width, f"宽度不匹配: {radar_norm.shape[3]} != {expected_width}"
    
    print("✓ 数据一致性测试通过")

def test_encoding_simulation():
    """模拟VAE编码过程"""
    print("\n=== 模拟VAE编码过程 ===")
    
    # 模拟输入: 3通道雷达数据
    batch_size = 2
    height, width = 300, 1280
    radar_3ch = np.random.randn(batch_size, 3, height, width) * 0.5
    print(f"3通道雷达输入: {radar_3ch.shape}")
    
    # 模拟VAE编码器输出 (通常会下采样8倍)
    latent_h, latent_w = height // 8, width // 8
    radar_latent = np.random.randn(batch_size, 4, latent_h, latent_w) * 0.1
    print(f"雷达潜在特征: {radar_latent.shape}")
    
    # 验证尺寸关系
    assert radar_latent.shape[2] == height // 8, f"潜在高度错误: {radar_latent.shape[2]} != {height // 8}"
    assert radar_latent.shape[3] == width // 8, f"潜在宽度错误: {radar_latent.shape[3]} != {width // 8}"
    
    print("✓ VAE编码模拟测试通过")
    return radar_latent

def main():
    """主测试函数"""
    print("开始雷达数据处理功能测试 (NumPy版本)...\n")
    
    try:
        # 测试雷达数据处理
        radar_tensor = test_radar_data_processing()
        
        # 测试雷达图像堆叠
        stacked_radar = test_radar_stacking()
        
        # 测试特征融合
        fused_features = test_feature_fusion()
        
        # 测试回退融合
        fallback_features = test_fallback_fusion()
        
        # 测试数据一致性
        test_data_consistency()
        
        # 模拟VAE编码
        radar_latent = test_encoding_simulation()
        
        print("\n=== 所有测试完成 ===")
        print("✅ 雷达数据处理功能验证通过")
        print("\n核心功能总结:")
        print("1. ✅ 雷达数据解码和归一化")
        print("2. ✅ 单通道到3通道堆叠")
        print("3. ✅ 多模态特征融合 (RGB + 深度 + 雷达)")
        print("4. ✅ 无雷达数据时的回退处理")
        print("5. ✅ 数据格式和范围验证")
        print("6. ✅ VAE编码过程模拟")
        
        print("\n数据流程验证:")
        print(f"原始雷达: (720, 1280) -> 裁剪: (300, 1280) -> 归一化: [-1, 1]")
        print(f"堆叠: [B, 1, H, W] -> [B, 3, H, W]")
        print(f"VAE编码: [B, 3, H, W] -> [B, 4, h, w]")
        print(f"特征融合: RGB[B,4,h,w] + 深度[B,4,h,w] + 雷达[B,4,h,w] -> [B,12,h,w]")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
