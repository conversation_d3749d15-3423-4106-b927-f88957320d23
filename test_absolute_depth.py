#!/usr/bin/env python3
"""
测试绝对深度预测功能
Test absolute depth prediction functionality

验证修改后的模型是否能正确预测绝对深度值
"""

import torch
import numpy as np
from omegaconf import OmegaConf

def test_absolute_depth_normalizer():
    """测试绝对深度归一化器"""
    print("=== 测试绝对深度归一化器 ===")
    
    try:
        from src.util.absolute_depth_transform import AbsoluteDepthNormalizer
        
        # 创建归一化器
        normalizer = AbsoluteDepthNormalizer(
            depth_min=0.1,
            depth_max=100.0,
            norm_min=-1.0,
            norm_max=1.0
        )
        
        # 测试数据
        test_depths = torch.tensor([0.1, 1.0, 10.0, 50.0, 100.0])
        print(f"测试深度值: {test_depths} 米")
        
        # 归一化
        normalized = normalizer(test_depths)
        print(f"归一化结果: {normalized}")
        
        # 反归一化
        denormalized = normalizer.denormalize(normalized)
        print(f"反归一化结果: {denormalized} 米")
        
        # 验证精度
        error = torch.abs(test_depths - denormalized).max()
        print(f"最大误差: {error:.6f}")
        
        assert error < 1e-5, "归一化/反归一化误差过大"
        print("✅ 绝对深度归一化器测试通过")
        
        return normalizer
        
    except Exception as e:
        print(f"❌ 绝对深度归一化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_depth_normalizer_registration():
    """测试深度归一化器注册"""
    print("\n=== 测试深度归一化器注册 ===")
    
    try:
        from src.util.depth_transform import get_depth_normalizer
        
        # 创建绝对深度配置
        cfg = OmegaConf.create({
            'type': 'absolute_depth',
            'depth_min': 0.1,
            'depth_max': 100.0,
            'norm_min': -1.0,
            'norm_max': 1.0,
            'clip': True
        })
        
        # 获取归一化器
        normalizer = get_depth_normalizer(cfg)
        print(f"归一化器类型: {type(normalizer)}")
        
        # 测试功能
        test_depth = torch.tensor([5.0, 25.0, 75.0])
        normalized = normalizer(test_depth)
        denormalized = normalizer.denormalize(normalized)
        
        print(f"测试深度: {test_depth}")
        print(f"归一化: {normalized}")
        print(f"反归一化: {denormalized}")
        
        error = torch.abs(test_depth - denormalized).max()
        assert error < 1e-5, "注册的归一化器功能异常"
        
        print("✅ 深度归一化器注册测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 深度归一化器注册测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pipeline_depth_normalizer():
    """测试管道深度归一化器设置"""
    print("\n=== 测试管道深度归一化器设置 ===")
    
    try:
        from marigold.marigold_pipeline import MarigoldPipeline
        from src.util.absolute_depth_transform import AbsoluteDepthNormalizer
        
        # 创建模拟管道（不加载实际模型）
        print("测试管道深度归一化器接口...")
        
        # 创建归一化器
        normalizer = AbsoluteDepthNormalizer(
            depth_min=0.1,
            depth_max=100.0
        )
        
        # 验证归一化器方法
        assert hasattr(normalizer, 'denormalize'), "归一化器缺少denormalize方法"
        assert hasattr(normalizer, 'get_depth_range'), "归一化器缺少get_depth_range方法"
        
        depth_min, depth_max = normalizer.get_depth_range()
        print(f"深度范围: [{depth_min}, {depth_max}] 米")
        
        print("✅ 管道深度归一化器接口测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 管道深度归一化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置文件加载"""
    print("\n=== 测试配置文件加载 ===")
    
    try:
        import os
        from omegaconf import OmegaConf
        
        config_path = "config/train_zju_radar.yaml"
        if os.path.exists(config_path):
            cfg = OmegaConf.load(config_path)
            
            # 检查深度归一化配置
            if hasattr(cfg, 'depth_normalization'):
                depth_cfg = cfg.depth_normalization
                print(f"深度归一化类型: {depth_cfg.type}")
                print(f"深度范围: [{depth_cfg.depth_min}, {depth_cfg.depth_max}]")
                print(f"归一化范围: [{depth_cfg.norm_min}, {depth_cfg.norm_max}]")
                
                # 验证配置
                assert depth_cfg.type == 'absolute_depth', "配置类型不正确"
                assert depth_cfg.depth_min == 0.1, "最小深度配置不正确"
                assert depth_cfg.depth_max == 100.0, "最大深度配置不正确"
                
                print("✅ 配置文件加载测试通过")
                return True
            else:
                print("❌ 配置文件中缺少depth_normalization")
                return False
        else:
            print(f"❌ 配置文件不存在: {config_path}")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_depth_range_conversion():
    """测试深度范围转换"""
    print("\n=== 测试深度范围转换 ===")
    
    try:
        from src.util.absolute_depth_transform import AbsoluteDepthNormalizer
        
        normalizer = AbsoluteDepthNormalizer(
            depth_min=0.1,
            depth_max=100.0,
            norm_min=-1.0,
            norm_max=1.0
        )
        
        # 测试边界值
        boundary_depths = torch.tensor([0.1, 100.0])  # 边界值
        boundary_norm = normalizer(boundary_depths)
        expected_norm = torch.tensor([-1.0, 1.0])
        
        print(f"边界深度: {boundary_depths}")
        print(f"边界归一化: {boundary_norm}")
        print(f"期望归一化: {expected_norm}")
        
        assert torch.allclose(boundary_norm, expected_norm, atol=1e-5), "边界值转换不正确"
        
        # 测试中间值
        mid_depth = torch.tensor([50.05])  # 中间值
        mid_norm = normalizer(mid_depth)
        expected_mid = torch.tensor([0.0])  # 应该接近0
        
        print(f"中间深度: {mid_depth}")
        print(f"中间归一化: {mid_norm}")
        
        assert torch.allclose(mid_norm, expected_mid, atol=0.01), "中间值转换不正确"
        
        print("✅ 深度范围转换测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 深度范围转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始绝对深度预测功能测试...\n")
    
    success = True
    
    # 测试绝对深度归一化器
    normalizer = test_absolute_depth_normalizer()
    success &= (normalizer is not None)
    
    # 测试深度归一化器注册
    success &= test_depth_normalizer_registration()
    
    # 测试管道接口
    success &= test_pipeline_depth_normalizer()
    
    # 测试配置文件
    success &= test_config_loading()
    
    # 测试深度范围转换
    success &= test_depth_range_conversion()
    
    if success:
        print("\n🎉 所有绝对深度预测测试通过！")
        print("\n功能验证:")
        print("1. ✅ 绝对深度归一化器实现")
        print("2. ✅ 深度归一化器注册机制")
        print("3. ✅ 管道深度归一化器接口")
        print("4. ✅ 配置文件支持绝对深度")
        print("5. ✅ 深度范围转换正确性")
        
        print("\n修改总结:")
        print("✅ 模型现在可以预测绝对深度值（单位：米）")
        print("✅ 深度范围: [0.1, 100.0] 米")
        print("✅ 验证时不再需要深度对齐")
        print("✅ 输出直接是物理距离值")
        
    else:
        print("\n❌ 部分绝对深度预测测试失败")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
