#!/usr/bin/env python3
"""
启动绝对深度预测训练
Start absolute depth prediction training

基于修改后的配置文件开始训练ZJU雷达数据集
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_prerequisites():
    """检查训练前提条件"""
    print("=== 检查训练前提条件 ===")
    
    # 检查配置文件
    config_file = "config/train_marigold_zju.yaml"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    print(f"✅ 配置文件存在: {config_file}")
    
    # 检查数据目录
    data_dir = "/data/Baidu/ZJU-4DRadarCam/data/"
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    print(f"✅ 数据目录存在: {data_dir}")
    
    # 检查数据列表文件
    train_list = "data_split/zju/train_list.txt"
    if not os.path.exists(train_list):
        print(f"❌ 训练列表不存在: {train_list}")
        return False
    
    with open(train_list, 'r') as f:
        train_count = len(f.readlines())
    print(f"✅ 训练样本数: {train_count}")
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"✅ GPU: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            print("❌ CUDA不可用")
            return False
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    
    return True

def test_data_loading():
    """快速测试数据加载"""
    print("\n=== 测试数据加载 ===")
    
    try:
        sys.path.append('.')
        from train_zju import ZJUDataset
        from src.dataset import DatasetMode
        from omegaconf import OmegaConf
        from src.util.depth_transform import get_depth_normalizer
        
        # 加载配置
        cfg = OmegaConf.load("config/train_marigold_zju.yaml")
        
        # 创建深度归一化器
        depth_transform = get_depth_normalizer(cfg.depth_normalization)
        print(f"深度归一化器: {type(depth_transform).__name__}")
        
        # 测试数据集
        dataset = ZJUDataset(
            mode=DatasetMode.TRAIN,
            filename_ls_path="data_split/zju/train_list.txt",
            dataset_dir="/data/Baidu/ZJU-4DRadarCam/data/",
            disp_name="zju_test",
            depth_transform=depth_transform,
            resize_to_hw=[300, 1280]
        )
        
        # 测试一个样本
        sample = dataset[0]
        rasters = sample[0] if len(sample) == 2 else sample
        
        print("数据键值:", list(rasters.keys()))
        
        # 检查关键数据
        required_keys = ["rgb_norm", "depth_raw_norm", "radar_norm"]
        for key in required_keys:
            if key in rasters:
                data = rasters[key]
                print(f"✅ {key}: {data.shape}, range: [{data.min():.3f}, {data.max():.3f}]")
            else:
                print(f"❌ 缺少数据: {key}")
                return False
        
        print("✅ 数据加载测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_training():
    """启动训练"""
    print("\n=== 启动绝对深度预测训练 ===")
    
    # 创建输出目录
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_dir = f"./output/absolute_depth_training_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    # 构建训练命令
    cmd = [
        "python", "train_zju.py",
        "--config", "config/train_marigold_zju.yaml",
        "--base_data_dir", "/data/Baidu/ZJU-4DRadarCam/data/",
        "--base_ckpt_dir", "./checkpoint",
        "--output_dir", output_dir,
        "--add_datetime_prefix"
    ]
    
    print(f"训练命令: {' '.join(cmd)}")
    print("\n开始训练...")
    print("=" * 60)
    
    # 启动训练
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 训练完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败，退出码: {e.returncode}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
        return False

def print_training_info():
    """打印训练信息"""
    print("🚀 ZJU 4D雷达-相机数据集绝对深度预测训练")
    print("=" * 60)
    print("训练特点:")
    print("• 多模态融合: RGB + 深度 + 雷达")
    print("• 绝对深度预测: 输出单位为米的物理距离")
    print("• 深度范围: [0.1, 100.0] 米")
    print("• 12通道UNet输入: RGB(4) + 深度(4) + 雷达(4)")
    print("• 无需深度对齐: 直接预测绝对深度值")
    print("=" * 60)

def main():
    """主函数"""
    print_training_info()
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 前提条件检查失败，无法开始训练")
        return False
    
    # 测试数据加载
    if not test_data_loading():
        print("\n❌ 数据加载测试失败，无法开始训练")
        return False
    
    # 确认开始训练
    print("\n准备开始训练...")
    print("配置摘要:")
    print("• 绝对深度预测: 启用")
    print("• 深度范围: [0.1, 100.0] 米")
    print("• 批次大小: 8 (有效)")
    print("• 最大迭代: 20,000")
    print("• 学习率: 3e-5")
    
    response = input("\n是否开始训练? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("训练已取消")
        return False
    
    # 开始训练
    success = start_training()
    
    if success:
        print("\n🎉 训练启动成功！")
        print("\n监控训练进度:")
        print("• 检查输出目录中的日志文件")
        print("• 使用 nvidia-smi 监控GPU使用情况")
        print("• 训练过程中会定期保存检查点")
        
        print("\n预期结果:")
        print("• 模型将学习预测绝对深度值")
        print("• 验证时无需深度对齐")
        print("• 输出深度图单位为米")
    else:
        print("\n❌ 训练启动失败")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
