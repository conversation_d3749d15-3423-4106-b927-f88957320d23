#!/usr/bin/env python3
"""
测试统一归一化
Test unified normalization

验证雷达和深度数据使用相同的归一化策略
"""

import torch
import numpy as np
from omegaconf import OmegaConf

def test_depth_normalizer():
    """测试深度归一化器"""
    print("=== 测试深度归一化器 ===")
    
    try:
        from src.util.absolute_depth_transform import AbsoluteDepthNormalizer
        
        # 创建深度归一化器 (与配置文件一致)
        depth_normalizer = AbsoluteDepthNormalizer(
            depth_min=0.1,
            depth_max=100.0,
            norm_min=-1.0,
            norm_max=1.0,
            clip=True
        )
        
        # 测试深度值
        test_depths = torch.tensor([0.1, 1.0, 10.0, 50.0, 100.0])
        normalized_depths = depth_normalizer(test_depths)
        denormalized_depths = depth_normalizer.denormalize(normalized_depths)
        
        print(f"深度归一化器:")
        print(f"  输入深度: {test_depths}")
        print(f"  归一化后: {normalized_depths}")
        print(f"  反归一化: {denormalized_depths}")
        print(f"  误差: {torch.abs(test_depths - denormalized_depths).max():.6f}")
        
        return depth_normalizer
        
    except Exception as e:
        print(f"❌ 深度归一化器测试失败: {e}")
        return None

def test_radar_normalizer():
    """测试雷达归一化器"""
    print("\n=== 测试雷达归一化器 ===")
    
    try:
        from src.util.absolute_depth_transform import AbsoluteDepthNormalizer
        
        # 创建雷达归一化器 (与修改后的代码一致)
        radar_normalizer = AbsoluteDepthNormalizer(
            depth_min=0.0,
            depth_max=100.0,
            norm_min=-1.0,
            norm_max=1.0,
            clip=True
        )
        
        # 测试雷达值
        test_radars = torch.tensor([0.0, 1.0, 10.0, 50.0, 100.0])
        normalized_radars = radar_normalizer(test_radars)
        denormalized_radars = radar_normalizer.denormalize(normalized_radars)
        
        print(f"雷达归一化器:")
        print(f"  输入雷达: {test_radars}")
        print(f"  归一化后: {normalized_radars}")
        print(f"  反归一化: {denormalized_radars}")
        print(f"  误差: {torch.abs(test_radars - denormalized_radars).max():.6f}")
        
        return radar_normalizer
        
    except Exception as e:
        print(f"❌ 雷达归一化器测试失败: {e}")
        return None

def compare_normalizers(depth_normalizer, radar_normalizer):
    """比较两个归一化器的行为"""
    print("\n=== 比较归一化器 ===")
    
    # 测试重叠范围的值
    overlap_values = torch.tensor([1.0, 10.0, 50.0, 100.0])
    
    depth_norm = depth_normalizer(overlap_values)
    radar_norm = radar_normalizer(overlap_values)
    
    print(f"重叠范围测试:")
    print(f"  测试值: {overlap_values}")
    print(f"  深度归一化: {depth_norm}")
    print(f"  雷达归一化: {radar_norm}")
    
    # 检查100米处的值是否相同
    test_100m = torch.tensor([100.0])
    depth_100 = depth_normalizer(test_100m)
    radar_100 = radar_normalizer(test_100m)
    
    print(f"\n100米处对比:")
    print(f"  深度归一化: {depth_100.item():.6f}")
    print(f"  雷达归一化: {radar_100.item():.6f}")
    print(f"  差异: {torch.abs(depth_100 - radar_100).item():.6f}")
    
    # 检查边界值
    print(f"\n边界值对比:")
    print(f"  深度范围: [0.1, 100.0] -> [-1, 1]")
    print(f"  雷达范围: [0.0, 100.0] -> [-1, 1]")
    
    # 深度边界
    depth_min_norm = depth_normalizer(torch.tensor([0.1]))
    depth_max_norm = depth_normalizer(torch.tensor([100.0]))
    
    # 雷达边界
    radar_min_norm = radar_normalizer(torch.tensor([0.0]))
    radar_max_norm = radar_normalizer(torch.tensor([100.0]))
    
    print(f"  深度边界: [{depth_min_norm.item():.3f}, {depth_max_norm.item():.3f}]")
    print(f"  雷达边界: [{radar_min_norm.item():.3f}, {radar_max_norm.item():.3f}]")

def test_data_loading_with_unified_normalization():
    """测试使用统一归一化的数据加载"""
    print("\n=== 测试统一归一化数据加载 ===")
    
    try:
        import sys
        sys.path.append('.')
        
        from train_zju import ZJUDataset
        from src.dataset import DatasetMode
        from src.util.depth_transform import get_depth_normalizer
        
        # 加载配置
        cfg = OmegaConf.load("config/train_marigold_zju.yaml")
        
        # 创建深度归一化器
        depth_transform = get_depth_normalizer(cfg.depth_normalization)
        print(f"深度归一化器类型: {type(depth_transform).__name__}")
        
        # 创建数据集
        dataset = ZJUDataset(
            mode=DatasetMode.TRAIN,
            filename_ls_path="data_split/zju/train_list.txt",
            dataset_dir="/data/Baidu/ZJU-4DRadarCam/data/",
            disp_name="zju_test",
            depth_transform=depth_transform,
            resize_to_hw=[300, 1280]
        )
        
        # 测试数据样本
        sample = dataset[0]
        rasters = sample[0] if len(sample) == 2 else sample
        
        # 检查归一化后的数据范围
        depth_data = rasters["depth_raw_norm"]
        radar_data = rasters["radar_norm"]
        
        print(f"\n数据范围检查:")
        print(f"  深度数据: {depth_data.shape}, 范围: [{depth_data.min():.3f}, {depth_data.max():.3f}]")
        print(f"  雷达数据: {radar_data.shape}, 范围: [{radar_data.min():.3f}, {radar_data.max():.3f}]")
        
        # 检查是否都在[-1, 1]范围内
        depth_in_range = (depth_data >= -1.0) & (depth_data <= 1.0)
        radar_in_range = (radar_data >= -1.0) & (radar_data <= 1.0)
        
        print(f"  深度数据在[-1,1]范围内: {depth_in_range.all()}")
        print(f"  雷达数据在[-1,1]范围内: {radar_in_range.all()}")
        
        if depth_in_range.all() and radar_in_range.all():
            print("✅ 统一归一化数据加载测试通过")
            return True
        else:
            print("❌ 数据范围超出[-1,1]")
            return False
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始统一归一化测试...\n")
    
    success = True
    
    # 测试深度归一化器
    depth_normalizer = test_depth_normalizer()
    success &= (depth_normalizer is not None)
    
    # 测试雷达归一化器
    radar_normalizer = test_radar_normalizer()
    success &= (radar_normalizer is not None)
    
    if depth_normalizer and radar_normalizer:
        # 比较归一化器
        compare_normalizers(depth_normalizer, radar_normalizer)
    
    # 测试数据加载
    success &= test_data_loading_with_unified_normalization()
    
    if success:
        print("\n🎉 统一归一化测试通过！")
        print("\n修改总结:")
        print("✅ 雷达和深度数据都使用AbsoluteDepthNormalizer")
        print("✅ 雷达范围: [0.0, 100.0] 米 -> [-1, 1]")
        print("✅ 深度范围: [0.1, 100.0] 米 -> [-1, 1]")
        print("✅ 归一化策略统一，训练更稳定")
        
        print("\n注意事项:")
        print("• 雷达和深度在100米处归一化值相同")
        print("• 雷达从0米开始，深度从0.1米开始")
        print("• 两者都使用线性映射到[-1, 1]范围")
        
    else:
        print("\n❌ 统一归一化测试失败")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
