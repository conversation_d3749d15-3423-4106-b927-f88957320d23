# ZJU dataset training script for Marigold
# Modified for ZJU dataset with specific data format:
# - Image size: 1280x720
# - Valid region: [:, 720//3:720//4*3] = [:, 240:540]
# - Image data: 0-255.0 range float32, CHW format
# - GT depth: actual depth values (meters), encoded with 256.0 multiplier in PNG
#
# Based on the original Marigold training script
# Last modified: 2024-05-17
#
# Copyright 2023 Bingxin Ke, ETH Zurich. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------
# If you find this code useful, we kindly ask you to cite our paper in your work.
# Please find bibtex at: https://github.com/prs-eth/Marigold#-citation
# More information about the method can be found at https://marigoldmonodepth.github.io
# --------------------------------------------------------------------------

import argparse
import logging
import os
import shutil
from datetime import datetime, timedelta
from typing import List

import torch
import numpy as np
from omegaconf import OmegaConf
from torch.utils.data import ConcatDataset, DataLoader
from tqdm import tqdm

from marigold.marigold_pipeline import MarigoldPipeline
from src.dataset import BaseDepthDataset, DatasetMode, get_dataset
from src.dataset.base_depth_dataset import DepthFileNameMode
from src.dataset.mixed_sampler import MixedBatchSampler
from src.trainer import get_trainer_cls
from src.util.config_util import (
    find_value_in_omegaconf,
    recursive_load_config,
)
from src.util.depth_transform import (
    DepthNormalizerBase,
    get_depth_normalizer,
)
from src.util.logging_util import (
    config_logging,
    init_wandb,
    load_wandb_job_id,
    log_slurm_job_id,
    save_wandb_job_id,
    tb_logger,
)
from src.util.slurm_util import get_local_scratch_dir, is_on_slurm


class ZJUDataset(BaseDepthDataset):
    """
    ZJU dataset with specific data format:
    - Image size: 1280x720
    - Valid region: [:, 240:540] (height dimension)
    - Image data: 0-255.0 range float32, CHW format
    - GT depth: actual depth values (meters), encoded with 256.0 multiplier in PNG
    - Radar data: 1280x720 single channel, 256x int format, valid range ~300
    """
    def __init__(
        self,
        **kwargs,
    ) -> None:
        super().__init__(
            # ZJU data parameters
            min_depth=1e-5,
            max_depth=100.0,
            has_filled_depth=False,
            name_mode=DepthFileNameMode.rgb_i_d,
            **kwargs,
        )

        # ZJU specific parameters
        self.image_height = 720
        self.image_width = 1280
        self.valid_height_start = self.image_height // 3  # 240
        self.valid_height_end = self.image_height // 4 * 3  # 540

        # Radar data parameters
        self.radar_max_range = 100.0  # 雷达有效范围 (与配置文件一致)
        self.radar_scale_factor = 256.0  # 雷达数据编码倍数

        # 创建雷达数据专用的绝对深度归一化器
        # Create dedicated absolute depth normalizer for radar data
        from src.util.absolute_depth_transform import AbsoluteDepthNormalizer
        self.radar_normalizer = AbsoluteDepthNormalizer(
            depth_min=0.0,      # 雷达最小距离
            depth_max=100.0,    # 雷达最大距离 (与配置文件一致)
            norm_min=-1.0,      # 归一化最小值
            norm_max=1.0,       # 归一化最大值
            clip=True           # 裁剪超出范围的值
        )
        print(f"雷达归一化器初始化: 范围 [0.0, 100.0] 米 -> [-1, 1]")
        
    def _read_depth_file(self, rel_path):
        """
        Read depth file for ZJU dataset.
        GT depth is encoded with 256.0 multiplier in PNG format.
        """
        depth_in = self._read_image(rel_path)
        # Decode ZJU depth: PNG values / 256.0 to get actual depth in meters
        depth_decoded = depth_in.astype(np.float32) / 256.0
        return depth_decoded
        
    def _load_rgb_data(self, rgb_rel_path):
        """
        Load RGB data for ZJU dataset.
        Apply cropping to valid region and ensure CHW format.
        """
        # Load RGB image
        rgb_data = super()._load_rgb_data(rgb_rel_path)

        # Apply cropping to valid region [:, 240:540]
        for key, value in rgb_data.items():
            if isinstance(value, torch.Tensor) and len(value.shape) >= 2:
                # Crop height dimension to valid region
                rgb_data[key] = value[:, self.valid_height_start:self.valid_height_end, :]

        return rgb_data

    def _load_depth_data(self, depth_rel_path, filled_rel_path):
        """
        Load depth data for ZJU dataset.
        Apply cropping to valid region.
        """
        # Load depth data
        depth_data = super()._load_depth_data(depth_rel_path, filled_rel_path)

        # Apply cropping to valid region [:, 240:540]
        for key, value in depth_data.items():
            if isinstance(value, torch.Tensor) and len(value.shape) >= 2:
                # Crop height dimension to valid region
                depth_data[key] = value[:, self.valid_height_start:self.valid_height_end, :]

        return depth_data

    def _training_preprocess(self, rasters):
        """
        Override training preprocessing to handle ZJU specific requirements.
        """
        # Augmentation
        if self.augm_args is not None:
            rasters = self._augment_data(rasters)

        # Normalization
        rasters["depth_raw_norm"] = self.depth_transform(
            rasters["depth_raw_linear"], rasters["valid_mask_raw"]
        ).clone()
        rasters["depth_filled_norm"] = self.depth_transform(
            rasters["depth_filled_linear"], rasters["valid_mask_filled"]
        ).clone()

        # Set invalid pixel to far plane
        if self.move_invalid_to_far_plane:
            if self.depth_transform.far_plane_at_max:
                rasters["depth_filled_norm"][~rasters["valid_mask_filled"]] = (
                    self.depth_transform.norm_max
                )
            else:
                rasters["depth_filled_norm"][~rasters["valid_mask_filled"]] = (
                    self.depth_transform.norm_min
                )

        # Resize - ZJU specific handling
        if self.resize_to_hw is not None:
            from torchvision.transforms import InterpolationMode, Resize
            resize_transform = Resize(
                size=self.resize_to_hw, interpolation=InterpolationMode.NEAREST_EXACT
            )
            # Apply resize to all tensors including radar data
            rasters = {k: resize_transform(v) for k, v in rasters.items()}

        return rasters

    def _get_data_path(self, index):
        """
        Override to handle three-column format: image_path depth_path radar_path
        """
        filename_line = self.filenames[index]

        # Get data paths - now expecting 3 columns
        rgb_rel_path = filename_line[0]

        depth_rel_path, filled_rel_path, radar_rel_path = None, None, None
        if DatasetMode.RGB_ONLY != self.mode:
            depth_rel_path = filename_line[1]
            if len(filename_line) >= 3:
                radar_rel_path = filename_line[2]  # 雷达数据路径
            if self.has_filled_depth:
                filled_rel_path = filename_line[3] if len(filename_line) >= 4 else None

        return rgb_rel_path, depth_rel_path, filled_rel_path, radar_rel_path

    def _get_data_item(self, index):
        """
        Override to include radar data loading
        """
        rgb_rel_path, depth_rel_path, filled_rel_path, radar_rel_path = self._get_data_path(index=index)

        rasters = {}

        # RGB data
        rasters.update(self._load_rgb_data(rgb_rel_path=rgb_rel_path))

        # Depth data
        if DatasetMode.RGB_ONLY != self.mode:
            # load depth data
            depth_data = self._load_depth_data(
                depth_rel_path=depth_rel_path, filled_rel_path=filled_rel_path
            )
            rasters.update(depth_data)
            # valid mask
            rasters["valid_mask_raw"] = self._get_valid_mask(
                rasters["depth_raw_linear"]
            ).clone()
            rasters["valid_mask_filled"] = self._get_valid_mask(
                rasters["depth_filled_linear"]
            ).clone()

            # Radar data - 添加雷达数据加载
            if radar_rel_path is not None:
                radar_data = self._load_radar_data(radar_rel_path=radar_rel_path)
                rasters.update(radar_data)

        other = {"index": index, "rgb_relative_path": rgb_rel_path}
        if radar_rel_path is not None:
            other["radar_relative_path"] = radar_rel_path

        return rasters, other

    def _read_radar_file(self, rel_path):
        """
        Read radar file for ZJU dataset.
        Radar data is encoded with 256.0 multiplier in PNG format, single channel.
        Valid range is approximately 300.
        """
        radar_in = self._read_image(rel_path)

        # Handle single channel radar data
        if len(radar_in.shape) == 3 and radar_in.shape[2] == 1:
            radar_in = radar_in.squeeze(2)  # Remove channel dimension if present
        elif len(radar_in.shape) == 3:
            # If multi-channel, take first channel
            radar_in = radar_in[:, :, 0]

        # Decode radar data: PNG values / 256.0 to get actual range values
        radar_decoded = radar_in.astype(np.float32) / self.radar_scale_factor

        # Clip to valid range (0 to radar_max_range)
        radar_decoded = np.clip(radar_decoded, 0.0, self.radar_max_range)

        return radar_decoded

    def _load_radar_data(self, radar_rel_path):
        """
        Load radar data for ZJU dataset.
        Apply cropping to valid region and convert to appropriate format.

        雷达数据处理流程:
        1. 读取雷达PNG文件 (1280x720, 单通道, 256倍编码)
        2. 解码为实际距离值 (有效范围约100米)
        3. 裁剪到有效区域 [:, 240:540]
        4. 使用与深度数据相同的绝对深度归一化器进行归一化
        """
        # Read radar data
        radar_raw = self._read_radar_file(radar_rel_path).squeeze()
        radar_raw_linear = torch.from_numpy(radar_raw.copy()).float().unsqueeze(0)  # [1, H, W]

        # Apply cropping to valid region [:, 240:540]
        radar_cropped = radar_raw_linear[:, self.valid_height_start:self.valid_height_end, :]

        # 使用与深度数据相同的绝对深度归一化器
        # Use the same absolute depth normalizer as depth data
        if hasattr(self, 'radar_normalizer') and self.radar_normalizer is not None:
            # 使用专门的雷达归一化器
            radar_norm = self.radar_normalizer(radar_cropped)
        else:
            # 回退到原始归一化方法（兼容性）
            radar_norm = (radar_cropped / self.radar_max_range) * 2.0 - 1.0
            radar_norm = torch.clamp(radar_norm, -1.0, 1.0)

        outputs = {
            "radar_raw_linear": radar_cropped.clone(),  # 原始线性雷达数据
            "radar_norm": radar_norm.clone(),           # 归一化雷达数据，用于VAE编码
        }

        return outputs


# Register ZJU dataset
from src.dataset import dataset_name_class_dict
dataset_name_class_dict["zju"] = ZJUDataset


if "__main__" == __name__:
    t_start = datetime.now()
    print(f"ZJU training started at {t_start}")

    # -------------------- Arguments --------------------
    parser = argparse.ArgumentParser(description="Train Marigold on ZJU dataset!")
    parser.add_argument(
        "--config",
        type=str,
        default="config/train_marigold.yaml",
        help="Path to config file.",
    )
    parser.add_argument(
        "--resume_run",
        action="store",
        default=None,
        help="Path of checkpoint to be resumed. If given, will ignore --config, and checkpoint in the config",
    )
    parser.add_argument(
        "--output_dir", type=str, default=None, help="directory to save checkpoints"
    )
    parser.add_argument("--no_cuda", action="store_true", help="Do not use cuda.")
    parser.add_argument(
        "--exit_after",
        type=int,
        default=-1,
        help="Save checkpoint and exit after X minutes.",
    )
    parser.add_argument("--no_wandb", action="store_true", help="run without wandb")
    parser.add_argument(
        "--do_not_copy_data",
        action="store_true",
        help="On Slurm cluster, do not copy data to local scratch",
    )
    parser.add_argument(
        "--base_data_dir", type=str, default=None, help="directory of training data"
    )
    parser.add_argument(
        "--base_ckpt_dir",
        type=str,
        default=None,
        help="directory of pretrained checkpoint",
    )
    parser.add_argument(
        "--add_datetime_prefix",
        action="store_true",
        help="Add datetime to the output folder name",
    )

    args = parser.parse_args()
    resume_run = args.resume_run
    output_dir = args.output_dir
    base_data_dir = (
        args.base_data_dir
        if args.base_data_dir is not None
        else os.environ.get("BASE_DATA_DIR", "./data")
    )
    base_ckpt_dir = (
        args.base_ckpt_dir
        if args.base_ckpt_dir is not None
        else os.environ.get("BASE_CKPT_DIR", "./checkpoint")
    )

    # -------------------- Initialization --------------------
    # Resume previous run
    if resume_run is not None:
        print(f"Resume run: {resume_run}")
        out_dir_run = os.path.dirname(os.path.dirname(resume_run))
        job_name = os.path.basename(out_dir_run)
        # Resume config file
        cfg = OmegaConf.load(os.path.join(out_dir_run, "config.yaml"))
    else:
        # Run from start
        cfg = recursive_load_config(args.config)
        # Full job name
        pure_job_name = os.path.basename(args.config).split(".")[0]
        # Add time prefix
        if args.add_datetime_prefix:
            job_name = f"{t_start.strftime('%y_%m_%d-%H_%M_%S')}-{pure_job_name}-zju"
        else:
            job_name = f"{pure_job_name}-zju"

        # Output dir
        if output_dir is not None:
            out_dir_run = os.path.join(output_dir, job_name)
        else:
            out_dir_run = os.path.join("./output", job_name)
        os.makedirs(out_dir_run, exist_ok=False)

    cfg_data = cfg.dataset

    # Other directories
    out_dir_ckpt = os.path.join(out_dir_run, "checkpoint")
    if not os.path.exists(out_dir_ckpt):
        os.makedirs(out_dir_ckpt)
    out_dir_tb = os.path.join(out_dir_run, "tensorboard")
    if not os.path.exists(out_dir_tb):
        os.makedirs(out_dir_tb)
    out_dir_eval = os.path.join(out_dir_run, "evaluation")
    if not os.path.exists(out_dir_eval):
        os.makedirs(out_dir_eval)
    out_dir_vis = os.path.join(out_dir_run, "visualization")
    if not os.path.exists(out_dir_vis):
        os.makedirs(out_dir_vis)

    # -------------------- Logging settings --------------------
    config_logging(cfg.logging, out_dir=out_dir_run)
    logging.debug(f"config: {cfg}")

    # Initialize wandb
    if not args.no_wandb:
        if resume_run is not None:
            wandb_id = load_wandb_job_id(out_dir_run)
            wandb_cfg_dic = {
                "id": wandb_id,
                "resume": "must",
                **cfg.wandb,
            }
        else:
            wandb_cfg_dic = {
                "config": dict(cfg),
                "name": job_name,
                "mode": "online",
                **cfg.wandb,
            }
        wandb_cfg_dic.update({"dir": out_dir_run})
        wandb_run = init_wandb(enable=True, **wandb_cfg_dic)
        save_wandb_job_id(wandb_run, out_dir_run)
    else:
        init_wandb(enable=False)

    # Tensorboard (should be initialized after wandb)
    tb_logger.set_dir(out_dir_tb)

    log_slurm_job_id(step=0)

    # -------------------- Device --------------------
    cuda_avail = torch.cuda.is_available() and not args.no_cuda
    device = torch.device("cuda" if cuda_avail else "cpu")
    logging.info(f"device = {device}")

    # -------------------- Snapshot of code and config --------------------
    if resume_run is None:
        _output_path = os.path.join(out_dir_run, "config.yaml")
        with open(_output_path, "w+") as f:
            OmegaConf.save(config=cfg, f=f)
        logging.info(f"Config saved to {_output_path}")
        # Copy and tar code on the first run
        _temp_code_dir = os.path.join(out_dir_run, "code_tar")
        _code_snapshot_path = os.path.join(out_dir_run, "code_snapshot.tar")
        os.system(
            f"rsync --relative -arhvz --quiet --filter=':- .gitignore' --exclude '.git' . '{_temp_code_dir}'"
        )
        os.system(f"tar -cf {_code_snapshot_path} {_temp_code_dir}")
        os.system(f"rm -rf {_temp_code_dir}")
        logging.info(f"Code snapshot saved to: {_code_snapshot_path}")

    # -------------------- Copy data to local scratch (Slurm) --------------------
    if is_on_slurm() and (not args.do_not_copy_data):
        # local scratch dir
        original_data_dir = base_data_dir
        base_data_dir = os.path.join(get_local_scratch_dir(), "Marigold_data")
        # copy data
        required_data_list = find_value_in_omegaconf("dir", cfg_data)
        required_data_list = list(set(required_data_list))
        logging.info(f"Required_data_list: {required_data_list}")
        for d in tqdm(required_data_list, desc="Copy data to local scratch"):
            ori_dir = os.path.join(original_data_dir, d)
            dst_dir = os.path.join(base_data_dir, d)
            os.makedirs(os.path.dirname(dst_dir), exist_ok=True)
            if os.path.isfile(ori_dir):
                shutil.copyfile(ori_dir, dst_dir)
            elif os.path.isdir(ori_dir):
                shutil.copytree(ori_dir, dst_dir)
        logging.info(f"Data copied to: {base_data_dir}")

    # -------------------- Gradient accumulation steps --------------------
    eff_bs = cfg.dataloader.effective_batch_size
    accumulation_steps = eff_bs / cfg.dataloader.max_train_batch_size
    assert int(accumulation_steps) == accumulation_steps
    accumulation_steps = int(accumulation_steps)

    logging.info(
        f"Effective batch size: {eff_bs}, accumulation steps: {accumulation_steps}"
    )

    # -------------------- Data --------------------
    loader_seed = cfg.dataloader.seed
    if loader_seed is None:
        loader_generator = None
    else:
        loader_generator = torch.Generator().manual_seed(loader_seed)

    # Training dataset
    # 修改深度归一化策略：支持绝对深度预测
    # Modified depth normalization: support absolute depth prediction
    cfg_normalizer = cfg.depth_normalization

    # 为绝对深度预测设置固定的归一化范围
    # Set fixed normalization range for absolute depth prediction
    print("Configuring depth normalization for absolute depth prediction")
    print(f"Original depth range: min={getattr(cfg_normalizer, 'depth_min', 'auto')}, max={getattr(cfg_normalizer, 'depth_max', 'auto')}")

    depth_transform: DepthNormalizerBase = get_depth_normalizer(
        cfg_normalizer=cfg_normalizer
    )

    # Override dataset configuration to use ZJU dataset
    print("Using ZJU dataset for training")
    if hasattr(cfg_data.train, 'name') and cfg_data.train.name == "zju":
        # Use configuration from config file
        cfg_data_train_modified = cfg_data.train
    else:
        # Fallback configuration
        cfg_data_train_modified = OmegaConf.create({
            "name": "zju",
            "disp_name": "zju",
            "dir": "/data/Baidu/ZJU-4DRadarCam/data/",
            "filenames": "data_split/zju/train_list.txt",
            "resize_to_hw": [300, 1280],  # Resize to maintain aspect ratio after cropping
        })

    train_dataset: BaseDepthDataset = get_dataset(
        cfg_data_train_modified,
        base_data_dir=base_data_dir,
        mode=DatasetMode.TRAIN,
        augmentation_args=cfg.augmentation,
        depth_transform=depth_transform,
    )
    logging.debug("Augmentation: ", cfg.augmentation)

    train_loader = DataLoader(
        dataset=train_dataset,
        batch_size=cfg.dataloader.max_train_batch_size,
        num_workers=cfg.dataloader.num_workers,
        shuffle=True,
        generator=loader_generator,
    )

    # Validation dataset
    val_loaders: List[DataLoader] = []
    for _val_dic in cfg_data.val:
        # Use ZJU dataset for validation
        if hasattr(_val_dic, 'name') and _val_dic.name == "zju":
            _val_dic_modified = _val_dic
        else:
            _val_dic_modified = OmegaConf.create({
                "name": "zju",
                "disp_name": "zju_val",
                "dir": "/data/Baidu/ZJU-4DRadarCam/data/",
                "filenames": "data_split/zju/val_list.txt",
                "resize_to_hw": [300, 1280],
            })
        _val_dataset = get_dataset(
            _val_dic_modified,
            base_data_dir=base_data_dir,
            mode=DatasetMode.EVAL,
        )
        _val_loader = DataLoader(
            dataset=_val_dataset,
            batch_size=1,
            shuffle=False,
            num_workers=cfg.dataloader.num_workers,
        )
        val_loaders.append(_val_loader)

    # Visualization dataset
    vis_loaders: List[DataLoader] = []
    for _vis_dic in cfg_data.vis:
        # Use ZJU dataset for visualization
        if hasattr(_vis_dic, 'name') and _vis_dic.name == "zju":
            _vis_dic_modified = _vis_dic
        else:
            _vis_dic_modified = OmegaConf.create({
                "name": "zju",
                "disp_name": "zju_vis",
                "dir": "/data/Baidu/ZJU-4DRadarCam/data/",
                "filenames": "data_split/zju/val_list.txt",
                "resize_to_hw": [300, 1280],
            })
        _vis_dataset = get_dataset(
            _vis_dic_modified,
            base_data_dir=base_data_dir,
            mode=DatasetMode.EVAL,
        )
        _vis_loader = DataLoader(
            dataset=_vis_dataset,
            batch_size=1,
            shuffle=False,
            num_workers=cfg.dataloader.num_workers,
        )
        vis_loaders.append(_vis_loader)

    # -------------------- Model --------------------
    _pipeline_kwargs = cfg.pipeline.kwargs if cfg.pipeline.kwargs is not None else {}
    model = MarigoldPipeline.from_pretrained(
        "stabilityai/stable-diffusion-2", **_pipeline_kwargs
    )

    print(model)
    # -------------------- Trainer --------------------
    # Exit time
    if args.exit_after > 0:
        t_end = t_start + timedelta(minutes=args.exit_after)
        logging.info(f"Will exit at {t_end}")
    else:
        t_end = None

    trainer_cls = get_trainer_cls(cfg.trainer.name)
    logging.debug(f"Trainer: {trainer_cls}")
    trainer = trainer_cls(
        cfg=cfg,
        model=model,
        train_dataloader=train_loader,
        device=device,
        base_ckpt_dir=base_ckpt_dir,
        out_dir_ckpt=out_dir_ckpt,
        out_dir_eval=out_dir_eval,
        out_dir_vis=out_dir_vis,
        accumulation_steps=accumulation_steps,
        val_dataloaders=val_loaders,
        vis_dataloaders=vis_loaders,
    )

    # -------------------- Checkpoint --------------------
    if resume_run is not None:
        trainer.load_checkpoint(
            resume_run, load_trainer_state=True, resume_lr_scheduler=True
        )

    # -------------------- Training & Evaluation Loop --------------------
    try:
        trainer.train(t_end=t_end)
    except Exception as e:
        logging.exception(e)
