# 绝对深度预测修改总结 (Absolute Depth Prediction Modification Summary)

## 修改目标 (Modification Goal)

将模型从预测相对深度改为直接预测绝对深度值，输出单位为米的物理距离。

## 主要修改内容 (Main Modifications)

### 1. 新增绝对深度归一化器 (`src/util/absolute_depth_transform.py`)

#### 1.1 AbsoluteDepthNormalizer 类
```python
class AbsoluteDepthNormalizer(DepthNormalizerBase):
    """
    绝对深度归一化器
    将绝对深度值线性映射到[-1, 1]范围
    """
    def __init__(self, depth_min=0.1, depth_max=100.0, norm_min=-1.0, norm_max=1.0):
        # 线性变换: norm = scale * depth + shift
        self.scale = (norm_max - norm_min) / (depth_max - depth_min)
        self.shift = norm_min - self.scale * depth_min
    
    def __call__(self, depth, valid_mask=None):
        """归一化: [depth_min, depth_max] -> [norm_min, norm_max]"""
        return self.scale * depth + self.shift
    
    def denormalize(self, depth_norm):
        """反归一化: [norm_min, norm_max] -> [depth_min, depth_max]"""
        return (depth_norm - self.shift) / self.scale
```

**关键特性:**
- **固定范围**: 深度范围 [0.1, 100.0] 米
- **线性映射**: 简单的线性变换，保持深度关系
- **可逆变换**: 支持归一化和反归一化

### 2. 修改深度归一化器注册 (`src/util/depth_transform.py`)

```python
def get_depth_normalizer(cfg_normalizer):
    # ... 原有代码 ...
    elif "absolute_depth" == cfg_normalizer.type:
        # 绝对深度归一化器
        from .absolute_depth_transform import AbsoluteDepthNormalizer
        depth_transform = AbsoluteDepthNormalizer(
            depth_min=getattr(cfg_normalizer, 'depth_min', 0.1),
            depth_max=getattr(cfg_normalizer, 'depth_max', 100.0),
            norm_min=cfg_normalizer.norm_min,
            norm_max=cfg_normalizer.norm_max,
            clip=cfg_normalizer.clip,
        )
```

### 3. 修改配置文件 (`config/train_zju_radar.yaml`)

```yaml
# 深度归一化配置 - 绝对深度预测
depth_normalization:
  type: absolute_depth          # 使用绝对深度归一化
  norm_min: -1.0               # 归一化最小值
  norm_max: 1.0                # 归一化最大值
  depth_min: 0.1               # 绝对深度最小值 (米)
  depth_max: 100.0             # 绝对深度最大值 (米)
  clip: true                   # 裁剪超出范围的值
```

### 4. 修改推理管道 (`marigold/marigold_pipeline.py`)

#### 4.1 添加深度归一化器支持
```python
def __init__(self, ...):
    # ... 原有代码 ...
    # 初始化深度归一化器（用于绝对深度预测）
    self.depth_normalizer = None

def set_depth_normalizer(self, depth_normalizer):
    """设置深度归一化器，用于绝对深度预测"""
    self.depth_normalizer = depth_normalizer
```

#### 4.2 修改深度后处理
```python
def single_infer(self, ...):
    # ... 去噪过程 ...
    depth = self.decode_depth(depth_latent)
    depth = torch.clip(depth, -1.0, 1.0)
    
    # 检查是否需要转换为绝对深度
    if hasattr(self, 'depth_normalizer') and hasattr(self.depth_normalizer, 'denormalize'):
        # 使用深度归一化器进行反归一化，得到绝对深度值
        depth = self.depth_normalizer.denormalize(depth)
        print(f"Converted to absolute depth, range: [{depth.min():.3f}, {depth.max():.3f}] meters")
    else:
        # 保持原有的相对深度输出 [0, 1]
        depth = (depth + 1.0) / 2.0
    
    return depth
```

### 5. 修改训练器 (`src/trainer/marigold_trainer.py`)

#### 5.1 设置深度归一化器
```python
def __init__(self, ...):
    # ... 原有代码 ...
    # 设置深度归一化器到模型（用于绝对深度预测）
    if hasattr(self.model, 'set_depth_normalizer') and train_dataloader is not None:
        if hasattr(train_dataloader.dataset, 'depth_transform'):
            depth_normalizer = train_dataloader.dataset.depth_transform
            self.model.set_depth_normalizer(depth_normalizer)
```

#### 5.2 修改验证时的深度对齐
```python
def validate_single_dataset(self, ...):
    # ... 预测深度 ...
    
    # 检查是否需要深度对齐（绝对深度预测时可能不需要）
    if hasattr(self.model, 'depth_normalizer') and self.model.depth_normalizer is not None:
        # 绝对深度预测，不需要对齐
        print("Using absolute depth prediction, skipping alignment")
    elif "least_square" == self.cfg.eval.alignment:
        # 相对深度预测，需要对齐
        depth_pred, scale, shift = align_depth_least_square(...)
```

## 技术原理 (Technical Principles)

### 1. 深度值映射关系

**训练时:**
```
真值深度 (米) → 归一化 [-1, 1] → VAE编码 → 潜在空间训练
```

**推理时:**
```
潜在空间预测 → VAE解码 → 归一化深度 [-1, 1] → 反归一化 → 绝对深度 (米)
```

### 2. 线性变换公式

**归一化:**
```
norm = (depth - depth_min) / (depth_max - depth_min) * 2 - 1
     = scale * depth + shift
```

**反归一化:**
```
depth = (norm - shift) / scale
      = (norm + 1) / 2 * (depth_max - depth_min) + depth_min
```

其中:
- `scale = 2 / (depth_max - depth_min) = 2 / 99.9 ≈ 0.02002`
- `shift = -1 - scale * depth_min ≈ -1.002`

### 3. 深度范围设计

- **最小深度**: 0.1米 (避免除零和近距离噪声)
- **最大深度**: 100.0米 (覆盖自动驾驶场景的主要范围)
- **有效范围**: 99.9米的动态范围
- **精度**: 在100米范围内保持良好的数值精度

## 测试验证 (Testing & Validation)

### 1. 功能测试结果
```
✅ 绝对深度归一化器实现
✅ 深度归一化器注册机制  
✅ 管道深度归一化器接口
✅ 配置文件支持绝对深度
✅ 深度范围转换正确性
```

### 2. 数值精度验证
- **边界值测试**: 0.1米 → -1.0, 100.0米 → 1.0
- **中间值测试**: 50.05米 → 0.0
- **往返精度**: 最大误差 < 1e-6

### 3. 集成测试
- **数据加载**: 支持绝对深度配置
- **模型训练**: 使用绝对深度目标
- **推理输出**: 直接输出米为单位的深度值

## 使用方法 (Usage)

### 1. 训练绝对深度模型
```bash
python train_zju.py --config config/train_zju_radar.yaml
```

### 2. 推理获取绝对深度
```python
from marigold.marigold_pipeline import MarigoldPipeline

# 加载模型
model = MarigoldPipeline.from_pretrained("path/to/model")

# 设置深度归一化器（如果需要）
from src.util.absolute_depth_transform import AbsoluteDepthNormalizer
normalizer = AbsoluteDepthNormalizer(depth_min=0.1, depth_max=100.0)
model.set_depth_normalizer(normalizer)

# 推理
result = model(rgb_image, radar_image=radar_data)
depth_meters = result.depth_np  # 单位：米
```

## 优势与特点 (Advantages & Features)

### 1. 直接物理意义
- **输出单位**: 米 (物理距离)
- **无需后处理**: 不需要深度对齐
- **直接应用**: 可直接用于距离测量

### 2. 数值稳定性
- **固定范围**: 避免动态范围变化
- **线性映射**: 简单可靠的变换
- **精度保证**: 在有效范围内保持高精度

### 3. 向后兼容
- **配置驱动**: 通过配置文件控制
- **自动检测**: 自动识别绝对/相对深度模式
- **渐进迁移**: 可以逐步从相对深度迁移

## 注意事项 (Important Notes)

1. **训练数据要求**: 真值深度必须是正确的绝对深度值
2. **深度范围**: 超出[0.1, 100.0]米的深度会被裁剪
3. **模型重训**: 需要重新训练模型以适应绝对深度目标
4. **评估指标**: 验证时不再需要深度对齐步骤

现在模型可以直接预测绝对深度值，输出单位为米的物理距离！
