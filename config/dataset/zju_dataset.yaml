# ZJU 4D Radar-Camera Dataset Configuration
# 配置文件用于ZJU雷达-相机数据集训练
# 数据格式: image/xxx.png gt_interp/xxx.png radar_png/xxx.png

dataset:
  train:
    name: zju
    disp_name: zju_train
    dir: /data/Baidu/ZJU-4DRadarCam/data/
    filenames: data_split/zju/train_list.txt
    # 图像尺寸配置
    # 原始尺寸: 1280x720
    # 有效区域: [:, 240:540] (高度方向裁剪)
    # 调整后尺寸: 保持宽高比
    resize_to_hw: [300, 1280]  # [height, width] after cropping
    
    # 深度数据配置
    min_depth: 1e-5
    max_depth: 100.0
    has_filled_depth: false
    
    # 雷达数据配置
    radar_max_range: 300.0      # 雷达最大有效距离
    radar_scale_factor: 256.0   # 雷达数据编码倍数
    
  val:
    - name: zju
      disp_name: zju_val
      dir: /data/Baidu/ZJU-4DRadarCam/data/
      filenames: data_split/zju/val_list.txt
      resize_to_hw: [300, 1280]
      
  vis:
    - name: zju
      disp_name: zju_vis
      dir: /data/Baidu/ZJU-4DRadarCam/data/
      filenames: data_split/zju/val_list.txt
      resize_to_hw: [300, 1280]

# 数据加载器配置
dataloader:
  effective_batch_size: 4
  max_train_batch_size: 2
  num_workers: 4
  seed: 2024

# 数据增强配置
augmentation:
  # 随机水平翻转
  lr_flip_p: 0.5
  # 随机裁剪
  random_crop:
    height: 300
    width: 640
  # 颜色抖动
  color_jitter:
    brightness: 0.1
    contrast: 0.1
    saturation: 0.1
    hue: 0.05

# 深度归一化配置
depth_normalization:
  type: scale_shift_depth
  norm_min: -1.0
  norm_max: 1.0
  min_max_quantile: 0.02
  clip: true
