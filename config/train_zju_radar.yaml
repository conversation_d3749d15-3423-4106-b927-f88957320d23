# ZJU 4D Radar-Camera Dataset Training Configuration
# 配置文件用于ZJU雷达-相机数据集的Marigold模型训练
# 支持RGB + 深度 + 雷达数据的多模态融合

# 数据集配置
dataset:
  train:
    name: zju
    disp_name: zju_train_radar
    dir: /data/Baidu/ZJU-4DRadarCam/data/
    filenames: data_split/zju/train_list.txt
    resize_to_hw: [300, 1280]  # [height, width] 裁剪后调整尺寸
    
  val:
    - name: zju
      disp_name: zju_val_radar
      dir: /data/Baidu/ZJU-4DRadarCam/data/
      filenames: data_split/zju/val_list.txt
      resize_to_hw: [300, 1280]
      
  vis:
    - name: zju
      disp_name: zju_vis_radar
      dir: /data/Baidu/ZJU-4DRadarCam/data/
      filenames: data_split/zju/val_list.txt
      resize_to_hw: [300, 1280]

# 数据加载器配置
dataloader:
  effective_batch_size: 8      # 有效批次大小 (增加以提高训练稳定性)
  max_train_batch_size: 2      # 最大训练批次大小 (受GPU内存限制)
  num_workers: 8               # 数据加载工作进程数 (增加以提高数据加载速度)
  seed: 2024                   # 随机种子

# 数据增强配置
augmentation:
  lr_flip_p: 0.5              # 水平翻转概率
  random_crop:
    height: 300               # 裁剪高度 (保持与resize一致)
    width: 640                # 裁剪宽度 (1280的一半，增加数据多样性)
  # 颜色抖动 (轻微，避免影响深度估计)
  color_jitter:
    brightness: 0.05          # 亮度抖动
    contrast: 0.05            # 对比度抖动
    saturation: 0.05          # 饱和度抖动
    hue: 0.02                 # 色调抖动

# 深度归一化配置 - 绝对深度预测
# Depth normalization config - for absolute depth prediction
depth_normalization:
  type: absolute_depth          # 使用绝对深度归一化
  norm_min: -1.0               # 归一化最小值
  norm_max: 1.0                # 归一化最大值
  depth_min: 0.1               # 绝对深度最小值 (米)
  depth_max: 100.0             # 绝对深度最大值 (米)
  clip: true                   # 裁剪超出范围的值

# 训练器配置
trainer:
  name: marigold_trainer       # 使用Marigold训练器
  init_seed: 42               # 初始化随机种子
  
# 学习率配置
lr: 2e-5                      # 学习率 (略微提高以加快收敛)

# 学习率调度器配置
lr_scheduler:
  kwargs:
    total_iter: 30000         # 总迭代次数 (减少以进行快速测试)
    final_ratio: 0.1          # 最终学习率比例 (提高以保持学习能力)
    warmup_steps: 500         # 预热步数 (减少以快速进入主要训练)

# 管道配置
pipeline:
  kwargs:
    # 使用预训练的Stable Diffusion 2.0模型
    # 将通过训练器自动扩展输入通道以支持雷达数据

# 损失函数配置
loss:
  name: mse_loss              # 均方误差损失

# 评估配置
eval:
  alignment: least_square     # 深度对齐方法
  
# 验证配置
validation:
  init_seed: 2024            # 验证随机种子
  denoising_steps: 10        # 验证时的去噪步数 (快速验证)
  ensemble_size: 1           # 验证时的集成大小 (加快验证速度)
  processing_res: 768        # 验证时的处理分辨率
  match_input_res: true      # 匹配输入分辨率
  resample_method: bilinear  # 重采样方法
  val_period: 2000           # 验证周期 (每2000步验证一次)

# 日志配置
logging:
  level: INFO
  format: "%(asctime)s - %(levelname)s - %(name)s - %(funcName)s:%(lineno)d >> %(message)s"
  
# Wandb配置
wandb:
  project: marigold-zju-radar
  entity: your_wandb_entity
  tags: 
    - zju
    - radar
    - depth_completion
    - multimodal

# 检查点配置
checkpoint:
  save_period: 2000          # 保存周期 (更频繁保存以防止丢失)
  max_to_keep: 10            # 最大保存数量 (保存更多检查点)

# 可视化配置
visualization:
  save_period: 500           # 可视化保存周期 (更频繁可视化以监控训练)

# 训练监控配置
monitoring:
  log_period: 50             # 日志打印周期
  loss_window: 100           # 损失平滑窗口
  grad_clip: 1.0             # 梯度裁剪阈值

# 优化器配置
optimizer:
  type: AdamW                # 使用AdamW优化器
  weight_decay: 0.01         # 权重衰减
  betas: [0.9, 0.999]        # Adam参数
  eps: 1e-8                  # 数值稳定性参数
