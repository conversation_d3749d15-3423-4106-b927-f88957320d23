# ZJU 4D Radar-Camera Dataset Training Configuration
# 配置文件用于ZJU雷达-相机数据集的Marigold模型训练
# 支持RGB + 深度 + 雷达数据的多模态融合

# 数据集配置
dataset:
  train:
    name: zju
    disp_name: zju_train_radar
    dir: /data/Baidu/ZJU-4DRadarCam/data/
    filenames: data_split/zju/train_list.txt
    resize_to_hw: [300, 1280]  # [height, width] 裁剪后调整尺寸
    
  val:
    - name: zju
      disp_name: zju_val_radar
      dir: /data/Baidu/ZJU-4DRadarCam/data/
      filenames: data_split/zju/val_list.txt
      resize_to_hw: [300, 1280]
      
  vis:
    - name: zju
      disp_name: zju_vis_radar
      dir: /data/Baidu/ZJU-4DRadarCam/data/
      filenames: data_split/zju/val_list.txt
      resize_to_hw: [300, 1280]

# 数据加载器配置
dataloader:
  effective_batch_size: 4      # 有效批次大小
  max_train_batch_size: 2      # 最大训练批次大小
  num_workers: 4               # 数据加载工作进程数
  seed: 2024                   # 随机种子

# 数据增强配置
augmentation:
  lr_flip_p: 0.5              # 水平翻转概率
  random_crop:
    height: 300
    width: 640

# 深度归一化配置
depth_normalization:
  type: scale_shift_depth
  norm_min: -1.0
  norm_max: 1.0
  min_max_quantile: 0.02
  clip: true

# 训练器配置
trainer:
  name: marigold_trainer       # 使用Marigold训练器
  init_seed: 42               # 初始化随机种子
  
# 学习率配置
lr: 1e-5                      # 学习率

# 学习率调度器配置
lr_scheduler:
  kwargs:
    total_iter: 50000         # 总迭代次数
    final_ratio: 0.01         # 最终学习率比例
    warmup_steps: 1000        # 预热步数

# 管道配置
pipeline:
  kwargs:
    # 使用预训练的Stable Diffusion 2.0模型
    # 将通过训练器自动扩展输入通道以支持雷达数据

# 损失函数配置
loss:
  name: mse_loss              # 均方误差损失

# 评估配置
eval:
  alignment: least_square     # 深度对齐方法
  
# 验证配置
validation:
  init_seed: 2024            # 验证随机种子

# 日志配置
logging:
  level: INFO
  
# Wandb配置
wandb:
  project: marigold-zju-radar
  entity: your_wandb_entity
  tags: 
    - zju
    - radar
    - depth_completion
    - multimodal

# 检查点配置
checkpoint:
  save_period: 5000          # 保存周期
  max_to_keep: 5             # 最大保存数量

# 可视化配置
visualization:
  save_period: 1000          # 可视化保存周期
