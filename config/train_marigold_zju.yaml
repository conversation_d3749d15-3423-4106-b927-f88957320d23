base_config:
- config/logging.yaml
- config/wandb.yaml
- config/dataset/train_zju.yaml
- config/dataset/val_zju.yaml
- config/dataset/vis_zju.yaml
- config/model_sdv2.yaml


pipeline:
  name: MarigoldPipeline
  kwargs:
    scale_invariant: false    # 改为false，因为我们预测绝对深度
    shift_invariant: false    # 改为false，因为我们预测绝对深度

# depth_normalization:
#   type: scale_shift_depth
#   clip: true
#   norm_min: -1.0
#   norm_max: 1.0
#   min_max_quantile: 0.02
depth_normalization:
  type: absolute_depth          # 使用绝对深度归一化
  norm_min: -1.0               # 归一化最小值
  norm_max: 1.0                # 归一化最大值
  depth_min: 0.1               # 绝对深度最小值 (米)
  depth_max: 100.0             # 绝对深度最大值 (米)
  clip: true       

# Radar data processing configuration
radar_processing:
  enabled: true  # Enable radar data integration
  max_range: 100.0  # Maximum radar range in meters
  scale_factor: 256.0  # Radar data encoding scale factor
  normalization:
    norm_min: -1.0  # Normalized radar range minimum
    norm_max: 1.0   # Normalized radar range maximum

augmentation:
  lr_flip_p: 0.5

dataloader:
  num_workers: 8             # 减少以避免内存问题
  effective_batch_size: 8    # 减少批次大小以适应12通道输入
  max_train_batch_size: 2    # 保持不变
  seed: 2024  # to ensure continuity when resuming from checkpoint

# Training settings
trainer:
  name: MarigoldTrainer
  training_noise_scheduler:
    pretrained_path: 1e128c8891e52218b74cde8f26dbfc701cb99d79
  init_seed: 2024  # use null to train w/o seeding
  save_period: 50
  backup_period: 2000
  validation_period: 2000
  visualization_period: 4000

multi_res_noise:
  strength: 0.9
  annealed: true
  downscale_strategy: original

gt_depth_type: depth_raw_norm
gt_mask_type: valid_mask_raw

max_epoch: 200  # a large enough number
max_iter: 20000  # 减少迭代次数以进行快速测试

optimizer:
  name: Adam

loss:
  name: mse_loss
  kwargs:
    reduction: mean

lr: 3.0e-05
lr_scheduler:
  name: IterExponential
  kwargs:
    total_iter: 20000         # 匹配max_iter
    final_ratio: 0.1          # 提高最终学习率比例
    warmup_steps: 500         # 增加预热步数

# Validation (and visualization) settings
validation:
  denoising_steps: 50
  ensemble_size: 1  # simplified setting for on-training validation
  processing_res: 0
  match_input_res: false
  resample_method: bilinear
  main_val_metric: abs_relative_difference
  main_val_metric_goal: minimize
  init_seed: 2024

eval:
  alignment: none             # 绝对深度预测不需要对齐
  align_max_res: null
  eval_metrics:
  - abs_relative_difference
  - squared_relative_difference
  - rmse_linear
  - rmse_log
  - log10
  - delta1_acc
  - delta2_acc
  - delta3_acc
  - i_rmse
  - silog_rmse
