#!/usr/bin/env python3
"""
脚本用于更新ZJU数据集的文件列表，添加雷达数据路径
将格式从 "image/xxx.png gt_interp/xxx.png" 
更新为 "image/xxx.png gt_interp/xxx.png radar_png/xxx.png"
"""

import os

def update_file_list(file_path):
    """
    更新单个文件列表，添加雷达数据路径
    
    Args:
        file_path (str): 文件列表路径
    """
    print(f"正在更新文件: {file_path}")
    
    # 读取原始文件
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    # 更新每一行，添加雷达数据路径
    updated_lines = []
    for line in lines:
        line = line.strip()
        if line:  # 跳过空行
            parts = line.split()
            if len(parts) == 2:  # 当前格式: image_path depth_path
                image_path, depth_path = parts
                # 从image路径提取文件名
                filename = os.path.basename(image_path)
                radar_path = f"radar_png/{filename}"
                # 创建新的行: image_path depth_path radar_path
                updated_line = f"{image_path} {depth_path} {radar_path}\n"
                updated_lines.append(updated_line)
            elif len(parts) == 3:  # 已经是新格式
                updated_lines.append(line + '\n')
            else:
                print(f"警告: 跳过格式不正确的行: {line}")
    
    # 写回文件
    with open(file_path, 'w') as f:
        f.writelines(updated_lines)
    
    print(f"完成更新: {file_path}, 处理了 {len(updated_lines)} 行")

def main():
    """主函数，更新所有ZJU数据集文件列表"""
    
    # ZJU数据集文件列表路径
    zju_files = [
        "data_split/zju/train_list.txt",
        "data_split/zju/val_list.txt", 
        "data_split/zju/test_list.txt"
    ]
    
    for file_path in zju_files:
        if os.path.exists(file_path):
            # 创建备份
            backup_path = file_path + ".backup"
            if not os.path.exists(backup_path):
                os.system(f"cp {file_path} {backup_path}")
                print(f"创建备份: {backup_path}")
            
            # 更新文件
            update_file_list(file_path)
        else:
            print(f"文件不存在: {file_path}")
    
    print("所有文件列表更新完成!")

if __name__ == "__main__":
    main()
